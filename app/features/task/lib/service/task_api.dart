// ignore_for_file: no_leading_underscores_for_local_identifiers

import 'dart:async';

import 'package:gp_core/core.dart';
import 'package:gp_feat_task/models/notification/task_notification.dart';
import 'package:gp_feat_task/models/section/section.dart';
import 'package:gp_feat_task/models/section/section_ext.dart';
import 'package:gp_feat_task/models/tag/tag_model.dart';
import 'package:gp_feat_task/models/task/participants_model.dart';
import 'package:gp_feat_task/models/task/permissions_model.dart';
import 'package:gp_feat_task/models/task/search_task_api_model.dart';
import 'package:gp_feat_task/models/task/task.dart';
import 'package:gp_feat_task/models/task/task_response.dart';
import 'package:gp_feat_task/models/task/task_setting_show_model.dart';
import 'package:gp_feat_task/screens/components/task/filter_status_subtask/selection_with_switch_button_widget.dart';
import 'package:gp_feat_task/screens/mini-task/folder/model/task_folder.dart';
import 'package:gp_feat_task/screens/mini-task/project/model/task_project_mode.dart';
import 'package:gp_feat_task/screens/mini-task/project/model/task_project_model.dart';
import 'package:gp_feat_task/screens/mini-task/section/confirm_remove_section.dart';
import 'package:gp_feat_task/screens/mini-task/show_hide_subtask/show_subtask_donetask_ext.dart';
import 'package:gp_feat_task/screens/mini-task/task-list/model/task_list.dart';
import 'package:gp_feat_task/screens/task_collab/widgets/bottom_sheet/bottom_sheet_model_extension.dart';
import 'package:gp_feat_task/screens/task_details/upload/model/task_attachment_file.dart';
import 'package:gp_feat_task/screens/tasks/model/task_screen_argument.dart';

class TaskAPI {
  final ApiService _service =
      ApiService(Constants.taskDomain, gpDomainChecker: GPDomainChecker.task);

  TaskResponse<Task> _taskResponse(dynamic data) => TaskResponse<Task>.fromJson(
        data,
        (jsonData) => Task.fromJson(jsonData),
      );

  ApiResponse<Task> _apiResponse(dynamic data) => ApiResponse<Task>.fromJson(
        data,
        (jsonData) => Task.fromJson(jsonData),
      );

  ApiResponse<ParticipantsModel> _apiResponseParticipantsModel(dynamic data) =>
      ApiResponse<ParticipantsModel>.fromJson(
        data,
        (jsonData) => ParticipantsModel.fromJson(jsonData),
      );

  ApiResponse<PermissionsModel> _apiResponsePermissionModel(dynamic data) =>
      ApiResponse<PermissionsModel>.fromJson(
        data,
        (jsonData) => PermissionsModel.fromJson(jsonData),
      );

  ListDataAPIResponse<Task> _apiResponseSubTask(dynamic data) =>
      ListDataAPIResponse<Task>.fromJson(
          data, (jsonData) => Task.fromJson(jsonData));

  Future<ListAPIResponse<TaskNotificationModel>> getListNotification(
      {String nextLink = "", int limit = 30}) async {
    var params = {
      "workspace_id": Constants.workspaceId(),
      "limit": limit.toString()
    };
    if (nextLink.isNotEmpty) params.addAll(Uri.splitQueryString(nextLink));
    final response = await _service.getData(endPoint: "/notify", query: params);
    ListAPIResponse<TaskNotificationModel> result = ListAPIResponse.fromJson(
      response.data,
      (jsonData) => TaskNotificationModel.fromJson(jsonData),
    );
    return result;
  }

  Future<dynamic> markReadANotification(String notiId) async {
    final response =
        await _service.putData(endPoint: "/notifications/$notiId/read");
    return response;
  }

  Future<dynamic> markSeenAllNotifications() async {
    final response =
        await _service.putData(endPoint: "/notifications/seen_all");
    return response;
  }

  Future<dynamic> markReadAllNotifications() async {
    final response =
        await _service.putData(endPoint: "/notifications/read_all");
    return response;
  }

  Future<TaskResponse<Task>> getListTask({
    String nextLink = "",
    List<TaskStatus>? taskStatues,
    String? sectionId,
    TaskRole role = TaskRole.all,
    TaskSortType sortBy = TaskSortType.createdTimeDesc,
    String? query,
    int limit = 10,
    Map<String, dynamic>? dueDate,
    TaskScreenArguments? taskScreenArguments,
    String userId = "",
    List<int>? userIds,
    List<String>? tagIds,
    bool? includeSubtask,
    List<BottomSheetWithSwitchModel>? listCheckStatus,
  }) async {
    Map<String, dynamic> params = {
      // "workspace_id": Constants.workspaceId(),
      "section_id": sectionId,
      "sort": sortBy.value,
      "role": role.value,
      "limit": limit.toString(),
    };
    // add toJson to check status task done and subtask (from bottomshet)
    if (listCheckStatus != null) {
      params.addAll(listCheckStatus.toJson());
    }

    if (sectionId == sectionModelSearchTaskId ||
        (sectionId == null || sectionId.isEmpty)) {
      params.remove("section_id");
    }

    if (sectionId == sectionModelSearchTaskId) {
      params.addAll({
        "include_subtask": true,
      });
    }

    if (taskStatues != null) {
      params.remove("section_id");
      params.addAll({
        "status": (taskStatues).map((e) => e.value.toString()).join(","),
      });
    }

    if (tagIds != null && tagIds.isNotEmpty) {
      params.addAll({
        "tag_ids": tagIds.join(","),
      });
    }

    if (includeSubtask != null) {
      params.addAll({
        "include_subtask": includeSubtask,
      });
    }

    // Search
    if (query != null && query.isNotEmpty) params['query'] = query.trim();

    // duedate
    if (dueDate != null) {
      if (dueDate.containsKey("overdue")) {
        params.addAll({
          "overdue": dueDate['overdue'],
        });
      } else if (dueDate.containsKey("due_date_from") &&
          dueDate.containsKey("due_date_to")) {
        params.addAll({
          "due_date_from": dueDate['due_date_from'],
        });
        params.addAll({
          "due_date_to": dueDate['due_date_to'],
        });
      }
    }

    // userId
    if (userId.isNotEmpty) {
      params.addAll({
        "user_id": userId,
      });
    }

    // userIds
    if (userIds != null) {
      params.addAll({
        "user_ids": userIds,
      });
    }

    // taskScreenArguments
    if (taskScreenArguments != null) {
      if (taskScreenArguments.projectId != null &&
          taskScreenArguments.projectId!.isNotEmpty) {
        params.addAll({
          "project_id": taskScreenArguments.projectId!,
        });
        // BE mong muốn: chỉ dùng 1 trong 3 params
        params.remove("folder_id");
        params.remove("task_list_id");
      }
      if (taskScreenArguments.folderId != null &&
          taskScreenArguments.folderId!.isNotEmpty) {
        params.addAll({
          "folder_id": taskScreenArguments.folderId!,
        });
        // BE mong muốn: chỉ dùng 1 trong 3 params
        params.remove("project_id");
        params.remove("task_list_id");
      }
      if (taskScreenArguments.taskListId != null &&
          taskScreenArguments.taskListId!.isNotEmpty) {
        params.addAll({
          "task_list_id": taskScreenArguments.taskListId!,
        });
        // BE mong muốn: chỉ dùng 1 trong 3 params
        params.remove("project_id");
        params.remove("folder_id");
      }

      if (taskScreenArguments.isArchive != null) {
        params.addAll({
          "archive": taskScreenArguments.isArchive,
        });
        if (taskScreenArguments.isHomeless == true) {
          params.addAll({
            "task_list_id": "",
          });
          if (taskScreenArguments.isAllStatus == true) {
            params.remove("status");
          }
        }
      }
    }

    if (nextLink.isNotEmpty) params.addAll(Uri.splitQueryString(nextLink));

    final response = await _service.getData(endPoint: "/tasks", query: params);
    TaskResponse<Task> result = _taskResponse(response.data);
    return result;
  }

  Future<TaskResponse<Task>> getListTaskDependency(
      {required String taskListId,
      int? skip,
      String? query,
      String status = '0',
      int limit = 10}) async {
    Map<String, dynamic> params = {
      "task_list_id": taskListId,
      "skip": skip,
      "query": query,
      "limit": limit,
      "status": status,
    };

    final response = await _service.getData(endPoint: "/tasks", query: params);
    TaskResponse<Task> result = _taskResponse(response.data);
    return result;
  }

  Future<ListDataAPIResponse<Task>> saveListDependency({
    required String taskId,
    required List<Task> taskBlock,
    required List<Task> taskWaiting,
  }) async {
    List<String> listTaskWaiting = taskWaiting.map((e) => e.id).toList();
    List<String> listTaskBlock = taskBlock.map((e) => e.id).toList();
    final body = {
      "waiting_ids": listTaskWaiting,
      "blocking_ids": listTaskBlock,
    };
    final response = await _service.patchData(
        endPoint: "/tasks/$taskId/dependencies", body: body);
    ListDataAPIResponse<Task> result = _apiResponseSubTask(response.data);
    return result;
  }

  Future<ApiResponse<SearchTaskApiModel>> searchApiTaskProject(
      String taskListsId,
      {String? query,
      int limit = 30}) async {
    try {
      Map<String, dynamic> params = {
        "query": query,
        "limit": limit,
        "include_subtask": true,
      };

      final response = await _service.getData(
        endPoint: "/task-lists/$taskListsId/tasks",
        query: params,
      );
      ApiResponse<SearchTaskApiModel> result =
          ApiResponse<SearchTaskApiModel>.fromJson(
        response.data,
        (jsonData) => SearchTaskApiModel.fromJson(jsonData),
      );
      return result;
    } catch (e) {
      rethrow;
    }
  }

  Future<ApiResponse<Task>> changeSubTaskToTask(String idTask) async {
    try {
      final response = await _service.patchData(
        endPoint: "/tasks/$idTask/convert-to-task",
      );
      ApiResponse<Task> result = _apiResponse(response.data);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  Future<ListDataAPIResponse<Task>?> getDetailWaitingTask(String idTask) async {
    if (idTask.trim().isEmpty) return null;

    try {
      final response = await _service.getData(
        endPoint: "/tasks/$idTask/dependencies",
      );
      ListDataAPIResponse<Task> result = _apiResponseSubTask(response.data);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  Future<ApiResponse<Task>> createNewTask(Task task) async {
    Map<String, dynamic> body = task.toCreateTaskRequestBody();
    body.remove("id");
    if (body["rrule"] == "") body.remove("rrule");
    body["workspace_id"] = Constants.workspaceId();
    for (int i = body.keys.length - 1; i >= 0; i--) {
      if (body[body.keys.toList()[i]] == null) {
        body.remove(body.keys.toList()[i]);
      }
    }
    try {
      final response = await _service.postData(endPoint: "/tasks", body: body);
      ApiResponse<Task> result = _apiResponse(response.data);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  Future<ApiResponse<Task>> editTask(Task task) async {
    var body = task.toEditTaskRequestBody();
    for (int i = body.keys.length - 1; i >= 0; i--) {
      var value = body[body.keys.toList()[i]];
      if (value == null || (value is List && value.isEmpty)) {
        final String key = body.keys.toList()[i];
        if (key != 'attachment_files') {
          body.remove(body.keys.toList()[i]);
        }
      }
    }
    try {
      final response =
          await _service.patchData(endPoint: "/tasks/${task.id}", body: body);
      ApiResponse<Task> result = _apiResponse(response.data);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  Future<ApiResponse<Task>> editTaskProjectTaskList(
      {required String taskId,
      required String taskListId,
      required String sectionId}) async {
    final body = {
      'task_list_id': taskListId,
      'section_id': sectionId,
    };
    try {
      final response =
          await _service.patchData(endPoint: "/tasks/$taskId", body: body);
      ApiResponse<Task> result = _apiResponse(response.data);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  Future<ApiResponse<Task>> editTaskTitle(
      {required String taskId, required String title}) async {
    final body = {'title': title};
    try {
      final response =
          await _service.patchData(endPoint: "/tasks/$taskId", body: body);
      ApiResponse<Task> result = _apiResponse(response.data);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  Future<ApiResponse<Task>> editTaskDescription(
      {required String taskId,
      required String description,
      required bool isRtf}) async {
    final body = {'description': description, 'content_rtf': isRtf};
    try {
      final response =
          await _service.patchData(endPoint: "/tasks/$taskId", body: body);
      ApiResponse<Task> result = _apiResponse(response.data);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  Future<ApiResponse<Task>> editTaskListTag({
    required String taskId,
    required List<TagModel> listTag,
  }) async {
    List<String> listId = listTag.map((ele) => ele.id).toList();

    final body = {
      'tag_ids': listTag.isEmpty ? [] : listId,
    };
    try {
      final response =
          await _service.patchData(endPoint: "/tasks/$taskId", body: body);
      ApiResponse<Task> result = _apiResponse(response.data);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  Future<ApiResponse<Task>> editTaskAttachmentFiles(
      {required String taskId,
      required List<TaskAttachmentFile> attachmentFiles}) async {
    final body = {
      "attachment_files": attachmentFiles.isEmpty
          ? []
          : List<dynamic>.from((attachmentFiles).map((e) => e.toJson())),
    };
    try {
      final response =
          await _service.patchData(endPoint: "/tasks/$taskId", body: body);
      ApiResponse<Task> result = _apiResponse(response.data);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  Future<ApiResponse<Task>> editTaskPriority(
      {required String taskId, required int priority}) async {
    final body = {"priority": priority};
    try {
      final response =
          await _service.patchData(endPoint: "/tasks/$taskId", body: body);
      ApiResponse<Task> result = _apiResponse(response.data);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  Future<ApiResponse<Task>> editTaskProgress(
      {required String taskId, required int progress}) async {
    final body = {"status": progress};
    try {
      final response =
          await _service.patchData(endPoint: "/tasks/$taskId", body: body);
      ApiResponse<Task> result = _apiResponse(response.data);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  Future<ApiResponse<Task>> editTaskDueDate(
      {required String taskId,
      int? dueDate,
      int? startDate,
      String? rRule,
      RecurrenceSettings? recurrenceSettings}) async {
    Map<String, dynamic> body = {};
    body['start_date'] = startDate ?? 0;
    body['due_date'] = dueDate ?? 0;
    body['rrule'] = rRule;
    if (recurrenceSettings != null) {
      body['recurrence_settings'] = recurrenceSettings.toJson();
    }
    try {
      final response =
          await _service.patchData(endPoint: "/tasks/$taskId", body: body);
      ApiResponse<Task> result = _apiResponse(response.data);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  Future<ApiResponse<Task>> editTaskAssigneeAndWatcher({
    required String taskId,
    List<GPUserModel>? assignees,
    List<GPUserModel>? watchers,
  }) async {
    List<int?> listAssigneesId = [];
    List<int?> listWatchersId = [];
    if (assignees != null) {
      listAssigneesId = assignees.map((e) => e.id).toList();
    }
    if (watchers != null) {
      listWatchersId = watchers.map((e) => e.id).toList();
    }
    final body = {
      "assignees": listAssigneesId,
      "watchers": listWatchersId,
    };
    try {
      final response =
          await _service.patchData(endPoint: "/tasks/$taskId", body: body);
      ApiResponse<Task> result = _apiResponse(response.data);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  Future<ApiResponse<Task>> getTaskInfo(String taskId) async {
    try {
      final response = await _service.getData(endPoint: "/tasks/$taskId");
      ApiResponse<Task> result = _apiResponse(response.data);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  Future<ApiResponse<ParticipantsModel>> getParticipantsTask(
      String taskId) async {
    try {
      final response =
          await _service.getData(endPoint: "/tasks/$taskId/participants");
      ApiResponse<ParticipantsModel> result =
          _apiResponseParticipantsModel(response.data);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  Future<ApiResponse<PermissionsModel>> getPermissionTask(String taskId) async {
    try {
      final response =
          await _service.getData(endPoint: "/tasks/$taskId/permissions");
      ApiResponse<PermissionsModel> result =
          _apiResponsePermissionModel(response.data);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  Future<ListDataAPIResponse<Task>> getSubTaskInfo(String taskId) async {
    try {
      final response =
          await _service.getData(endPoint: "/tasks/$taskId/subtasks");
      ListDataAPIResponse<Task> result = _apiResponseSubTask(response.data);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response<dynamic>> deleteTask(String taskId) async {
    try {
      final response = await _service.delete(endPoint: "/tasks/$taskId");
      // Map<String, dynamic> _response = response.data;
      return response;
      // return _response
      //   ..addAll({
      //     'status_code': response.statusCode,
      //   });
    } catch (e) {
      rethrow;
    }
  }

  // ---------- Project ---------- \\
  Future<ListDataAPIResponse<Project>> getListProject({
    bool archive = false,
    bool dataOriginal = false,
  }) async {
    var params = {
      "archive": archive,
    };

    final response =
        await _service.getData(endPoint: "/projects", query: params);
    late ListDataAPIResponse<Project> result;
    if (dataOriginal) {
      result = ListDataAPIResponse<Project>.fromJson(
        response.data,
        (jsonData) => Project.fromJson(jsonData),
      );
    } else {
      result = ListDataAPIResponse<Project>.fromJson(
        response.data,
        (jsonData) => Project.fromJsonAddTaskListToFolder(jsonData),
      );
    }

    return result;
  }

  Future<ApiResponse<Project>> getDataProject({
    String? projectID,
    bool archive = false,
  }) async {
    var params = {
      "archive": archive,
    };

    final response =
        await _service.getData(endPoint: "/projects/$projectID", query: params);
    ApiResponse<Project> result = ApiResponse<Project>.fromJson(
      response.data,
      (jsonData) => Project.fromJson(jsonData),
    );
    return result;
  }

  Future archiveProject(String projectId, bool archive) async {
    var params = {
      "archive": archive,
    };

    final response = await _service.patchData(
        endPoint: "/projects/$projectId/archive", body: params);

    return Project.fromJson(response.data["data"]);
  }

  Future archiveFolder(String folderId, bool archive) async {
    var params = {
      "archive": archive,
    };

    final response = await _service.patchData(
        endPoint: "/folders/$folderId/archive", body: params);
    return Folder.fromJson(response.data["data"], FolderType.folder);
  }

  Future archiveTaskList(String taskListId, bool archive) async {
    var params = {
      "archive": archive,
    };

    final response = await _service.patchData(
        endPoint: "/task-lists/$taskListId/archive", body: params);
    return Folder.fromJson(response.data["data"], FolderType.folder);
  }

  Future archiveTask(List<String> taskIds, bool archive) async {
    var params = {
      "archive": archive,
      "task_ids": taskIds,
    };

    final response =
        await _service.patchData(endPoint: "/tasks/archive", body: params);
    return response;
  }

  Future<dynamic> createProject(Project project) async {
    Map<String, dynamic> body = project.toCreateJson();
    try {
      final response =
          await _service.postData(endPoint: "/projects", body: body);
      Map<String, dynamic> _response = response.data;
      return _response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Project> editProject(Project project) async {
    Map<String, dynamic> body = project.toCreateJson();
    try {
      final response = await _service.patchData(
          endPoint: "/projects/${project.id}", body: body);
      Project newProject = Project.fromJson(response.data["data"]);
      return newProject;
    } catch (e) {
      rethrow;
    }
  }

  Future<Project> addNewMemberProject({
    required String projectId,
    required List<int> memberIds,
    required List<int> ignoreMemberIds,
    required List<String> departmentIds,
    required List<String> roleIds,
    required List<String> threadIds,
  }) async {
    try {
      final response = await _service
          .patchData(endPoint: "/projects/$projectId/add-members", body: {
        "members": memberIds,
        "department_ids": departmentIds,
        "role_ids": roleIds,
        "thread_ids": threadIds,
        "ignore_user_ids": ignoreMemberIds,
      });
      Project newProject = Project.fromJson(response.data["data"]);
      return newProject;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response<dynamic>> deleteProject(String projectId) async {
    try {
      final response = await _service.delete(endPoint: "/projects/$projectId");
      // Map<String, dynamic> _response = response.data;
      return response;
      // return _response
      //   ..addAll({
      //     'status_code': response.statusCode,
      //   });
    } catch (e) {
      rethrow;
    }
  }

  Future<dynamic> createFolder(Map<String, dynamic> body) async {
    try {
      final response =
          await _service.postData(endPoint: "/folders", body: body);
      Map<String, dynamic> _response = response.data;
      return _response;
    } catch (e) {
      rethrow;
    }
  }

  Future<dynamic> editFolder(Folder folder) async {
    Map<String, dynamic> body = folder.toEditJson();
    try {
      final response = await _service.patchData(
          endPoint: "/folders/${folder.id}", body: body);
      Map<String, dynamic> _response = response.data;
      return _response;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response<dynamic>> deleteFolder(String folderId) async {
    try {
      final response = await _service.delete(endPoint: "/folders/$folderId");
      // Map<String, dynamic> _response = response.data;
      return response;
      // return _response
      //   ..addAll({
      //     'status_code': response.statusCode,
      //   });
    } catch (e) {
      rethrow;
    }
  }

  Future<dynamic> createTaskList(Map<String, dynamic> body) async {
    try {
      final response =
          await _service.postData(endPoint: "/task-lists", body: body);
      Map<String, dynamic> _response = response.data;
      return _response;
    } catch (e) {
      rethrow;
    }
  }

  Future<dynamic> editTaskList(TaskList taskList) async {
    Map<String, dynamic> body = taskList.toEditJson();
    try {
      final response = await _service.patchData(
          endPoint: "/task-lists/${taskList.id}", body: body);
      Map<String, dynamic> _response = response.data;
      return _response;
    } catch (e) {
      rethrow;
    }
  }

  Future<TaskList> getTaskListInfo(String taskListId) async {
    try {
      final response =
          await _service.getData(endPoint: "/task-lists/$taskListId");
      ApiResponse<TaskList> result = ApiResponse.fromJson(
        response.data,
        (jsonData) => TaskList.fromJson(jsonData),
      );
      return result.data;
    } catch (e) {
      rethrow;
    }
  }

  Future<Response<dynamic>> deleteTaskList(String taskListId) async {
    try {
      final response =
          await _service.delete(endPoint: "/task-lists/$taskListId");
      // Map<String, dynamic> _response = response.data;
      return response;
      // return _response
      //   ..addAll({
      //     'status_code': response.statusCode,
      //   });
    } catch (e) {
      rethrow;
    }
  }

  ///
  /// Load toàn bộ members trong 1 project
  ///
  /// `firstPageSuccessCallback`: callback khi load success page đầu tiên
  ///
  /// Không nên dùng callback khi load từng page để tránh việc render UI liên tục
  ///
  Future<ListAPIResponse<Assignee>> getFullProjectMembers({
    required String? projectId,
    Function(ListAPIResponse<Assignee>)? firstPageSuccessCallback,
  }) async {
    String nextLink = "";
    final List<Assignee> assignees = [];

    Future<ListAPIResponse<Assignee>> _fetchData() async {
      final response = await getProjectMembers(
        projectId: projectId,
        limit: 30,
        nextLink: nextLink,
      );

      // page đầu tiên
      if (nextLink.isEmpty) {
        firstPageSuccessCallback?.call(response);
      }

      nextLink = response.links?.next ?? "";
      assignees.addAll(response.data ?? []);

      if (nextLink.isNotEmpty) {
        // đệ quy load tiếp các pages tiếp theo
        return await _fetchData();
      }

      // sau khi load xong tất cả các pages
      return response;
    }

    // load full data, giữ lại response của page cuối cùng
    // `response.data` là data của toàn bộ pages
    final response = await _fetchData();
    response.data?.clear();
    response.data?.addAll(assignees);

    return response;
  }

  Future<ListAPIResponse<Assignee>> getProjectMembers({
    required String? projectId,
    required int limit,
    String nextLink = "",
  }) async {
    final Map<String, dynamic> params = {"limit": limit};

    if (nextLink.isNotEmpty) params.addAll(Uri.splitQueryString(nextLink));
    final response = await _service.getData(
        endPoint: "/projects/$projectId/members", query: params);

    ListAPIResponse<ProjectMember> projectResponse = ListAPIResponse.fromJson(
      response.data,
      (jsonData) => ProjectMember.fromJson(jsonData),
    );

    final assigneesBinding =
        (projectResponse.data ?? []).map((e) => e.toAssignee()).toList();

    final ListAPIResponse<Assignee> assignees = ListAPIResponse<Assignee>(
      code: projectResponse.code,
      data: assigneesBinding,
      links: projectResponse.links,
      message: projectResponse.message,
    );

    return assignees;
  }

  Future<Project> getProjectInfo(String projectId,
      {bool isArchive = false, bool groupTaskListWithFolder = false}) async {
    try {
      final response = await _service.getData(
          endPoint: "/projects/$projectId?archive=$isArchive");
      ApiResponse<Project> result = groupTaskListWithFolder
          ? ApiResponse.fromJson(
              response.data,
              (jsonData) => Project.fromJsonAddTaskListToFolder(jsonData),
            )
          : ApiResponse.fromJson(
              response.data,
              (jsonData) => Project.fromJson(jsonData),
            );
      return result.data;
    } catch (e) {
      rethrow;
    }
  }

  Future<dynamic> leaveProject(String projectId) async {
    try {
      final response =
          await _service.patchData(endPoint: "/projects/$projectId/leave");
      return response;
    } catch (e) {
      rethrow;
    }
  }

  Future<SectionModel> createSection(SectionModel section) async {
    try {
      final response = await _service.postData(
          endPoint: "/sections", body: section.toJson());
      final result = SectionModel.fromJson(response.data["data"]);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  Future<Task> duplicateTask(String taskId, {String? taskListId}) async {
    try {
      Map<String, dynamic> bodyParam = {};
      if (taskListId != null) {
        bodyParam = {'task_list_id': taskListId};
      }
      final response = await _service.postData(
          endPoint: "/tasks/$taskId/duplicate", body: bodyParam);
      return Task.fromJson(response.data["data"]);
    } catch (e) {
      rethrow;
    }
  }

  Future<SectionModel> editSection(SectionModel section) async {
    try {
      final response = await _service.patchData(
          endPoint: "/sections/${section.id}", body: section.toJson());
      final result = SectionModel.fromJson(response.data["data"]);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  Future<SectionModel> removeSection(
      SectionModel section, RemoveOption option) async {
    try {
      final response = await _service.delete(
          endPoint:
              "/sections/${section.id}?keep_task=${option == RemoveOption.removeButKeepTask}");
      final result = SectionModel.fromJson(response.data["data"]);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  Future<TaskList> getTaskInTaskList({
    required String taskListId,
    TaskRole role = TaskRole.all,
    TaskSortType sortBy = TaskSortType.createdTimeDesc,
    TaskScreenArguments? taskScreenArguments,
    List<int>? userIds,
    List<String>? tagIds,
    bool? includeSubtask,
    List<BottomSheetWithSwitchModel>? listCheckStatus,
  }) async {
    Map<String, dynamic> params = {
      "workspace_id": Constants.workspaceId(),
      "sort": sortBy.value,
      "role": role.value,
      "limit": "30",
    };
    // add toJson to check status task done and subtask (from bottomshet)
    if (listCheckStatus != null) {
      params.addAll(listCheckStatus.toJson());
    }
    // userIds
    if (userIds != null) {
      params.addAll({
        "user_ids": userIds,
      });
    }

    if (tagIds != null && tagIds.isNotEmpty) {
      params.addAll({
        "tag_ids": tagIds.join(","),
      });
    }

    if (includeSubtask != null) {
      params.addAll({
        "include_subtask": includeSubtask,
      });
    }

    try {
      final response = await _service.getData(
          endPoint: "/task-lists/$taskListId/tasks", query: params);
      ApiResponse<TaskList> result = ApiResponse.fromJson(
        response.data,
        (jsonData) => TaskList.fromJson(jsonData),
      );
      return result.data;
    } catch (e) {
      rethrow;
    }
  }

  Future<ApiResponse<Task>> updateTaskSection(
      {required Task task, required String sectionId}) async {
    try {
      final response =
          await _service.patchData(endPoint: "/tasks/${task.id}", body: {
        "section_id": sectionId,
      });
      ApiResponse<Task> result = _apiResponse(response.data);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  Future<void> changeCollabNotificationSettings(
      {required String collabId,
      required List<BottomSheetModel> settings}) async {
    try {
      await _service.patchData(
          endPoint: "/projects/$collabId",
          body: {"notification_settings": settings.toJson()});
    } catch (e) {
      rethrow;
    }
  }

  Future<Project> changeCollabVisibleSettings(
      {required String collabId, required bool visible}) async {
    try {
      final response = await _service.patchData(
          endPoint: "/projects/$collabId", body: {"visible": visible});
      ApiResponse<Project> result = ApiResponse.fromJson(
        response.data,
        (jsonData) => Project.fromJson(jsonData),
      );
      return result.data;
    } catch (e) {
      rethrow;
    }
  }

  Future<Project> activeCollabGroup({
    required String collabId,
  }) async {
    try {
      final response = await _service.patchData(
          endPoint: "/projects/$collabId", body: {"feature_status": true});
      ApiResponse<Project> result = ApiResponse.fromJson(
        response.data,
        (jsonData) => Project.fromJson(jsonData),
      );
      return result.data;
    } catch (e) {
      rethrow;
    }
  }

  Future<TaskList> changePositionTaskList({
    required String taskListId,
    String? folderId,
    String? projectId,
  }) async {
    try {
      final response =
          await _service.patchData(endPoint: "/task-lists/$taskListId", body: {
        "folder_id": folderId,
        "project_id": projectId,
      });
      ApiResponse<TaskList> result = ApiResponse.fromJson(
        response.data,
        (jsonData) => TaskList.fromJson(jsonData),
      );
      return result.data;
    } catch (e) {
      rethrow;
    }
  }

  Future<Folder> changePositionFolder({
    required String folderId,
    String? projectId,
  }) async {
    try {
      final response =
          await _service.patchData(endPoint: "/folders/$folderId", body: {
        "project_id": projectId,
      });
      ApiResponse<Folder> result = ApiResponse.fromJson(
        response.data,
        (jsonData) => Folder.fromJson(jsonData, FolderType.folder),
      );
      return result.data;
    } catch (e) {
      rethrow;
    }
  }

  Future<TaskSettingShowModel> getSettingShowTask() async {
    try {
      final response = await _service.getData(endPoint: "/views/settings");
      ApiResponse<TaskSettingShowModel> result = ApiResponse.fromJson(
        response.data,
        (jsonData) => TaskSettingShowModel.fromJson(jsonData),
      );
      return result.data;
    } catch (e) {
      rethrow;
    }
  }

  Future<void> putSettingShowTask(
      TaskSettingShowModel taskSettingShowModel) async {
    try {
      await _service.putData(
          endPoint: "/views/settings", body: taskSettingShowModel.toJson());
    } catch (e) {
      rethrow;
    }
  }

  Future<void> pinProject(String projectId, TaskProjectMode projectType) async {
    try {
      await _service.postData(
          endPoint: "/projects/$projectId/pin",
          body: {"project_type": projectType.toPinProjectType()});
    } catch (e) {
      rethrow;
    }
  }

  Future<void> unpinProject(
      String projectId, TaskProjectMode projectType) async {
    try {
      await _service.delete(
          endPoint: "/projects/$projectId/pin",
          query: {"project_type": projectType.toPinProjectType()});
    } catch (e) {
      rethrow;
    }
  }

  Future<RecurrenceSettings> getWorkscapeRecurrenceSettings() async {
    try {
      final response =
          await _service.getData(endPoint: "/tasks/settings/recurrence");
      return RecurrenceSettings.fromJson(response.data["data"]);
    } catch (e) {
      rethrow;
    }
  }
}
